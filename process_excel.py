#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理脚本
功能：
1. 接入DeepSeek API
2. 处理simplified_motivations_mapping.xlsx文件
3. 去除括号和括号内容
4. 将剩余内容压缩成机器学习标签形式
5. 将结果放在E列
"""

import pandas as pd
import re
import requests
import json
import time
from typing import List, Optional

class DeepSeekAPI:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_label(self, text: str) -> str:
        """
        使用DeepSeek API将文本转换为机器学习标签
        """
        # 先去除括号和括号内容
        cleaned_text = self.remove_brackets(text)
        
        if not cleaned_text.strip():
            return ""
        
        # 构建提示词
        prompt = f"""
请将以下文本转换为简洁的机器学习标签格式。要求：
1. 保留核心含义
2. 使用简短的词汇或短语
3. 适合作为分类标签
4. 如果是多个概念，用下划线连接
5. 使用英文或中文都可以，但要保持一致性

原文本：{cleaned_text}

标签：
"""
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 100
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                label = result['choices'][0]['message']['content'].strip()
                # 清理标签，去除多余的格式
                label = re.sub(r'^标签[：:]\s*', '', label)
                label = re.sub(r'^Label[：:]\s*', '', label, flags=re.IGNORECASE)
                return label
            else:
                print(f"API请求失败: {response.status_code}, {response.text}")
                return cleaned_text  # 如果API失败，返回清理后的原文本
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return cleaned_text  # 如果API失败，返回清理后的原文本
    
    @staticmethod
    def remove_brackets(text: str) -> str:
        """
        去除文本中的括号和括号内容
        支持：() [] {} 《》 「」 【】
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""
        
        # 定义括号对
        bracket_patterns = [
            r'\([^)]*\)',      # 圆括号 ()
            r'\[[^\]]*\]',     # 方括号 []
            r'\{[^}]*\}',      # 花括号 {}
            r'《[^》]*》',      # 书名号 《》
            r'「[^」]*」',      # 日式引号 「」
            r'【[^】]*】',      # 方头括号 【】
        ]
        
        result = text
        for pattern in bracket_patterns:
            result = re.sub(pattern, '', result)
        
        # 清理多余的空格
        result = re.sub(r'\s+', ' ', result).strip()
        return result

def process_excel_file(file_path: str, api_key: str, output_path: str = None):
    """
    处理Excel文件的主函数
    """
    print(f"开始处理文件: {file_path}")
    
    # 读取Excel文件
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return
    
    # 初始化DeepSeek API
    api_client = DeepSeekAPI(api_key)
    
    # 创建E列用于存储标签
    df['E'] = ""
    
    # 处理每一行数据
    for index, row in df.iterrows():
        print(f"处理第 {index + 1} 行...")
        
        # 假设要处理的文本在某一列，这里需要根据实际情况调整
        # 先检查哪些列包含文本数据
        text_to_process = ""
        for col in df.columns:
            if col != 'E' and pd.notna(row[col]) and isinstance(row[col], str):
                if text_to_process:
                    text_to_process += " " + str(row[col])
                else:
                    text_to_process = str(row[col])
        
        if text_to_process.strip():
            # 生成标签
            label = api_client.generate_label(text_to_process)
            df.at[index, 'E'] = label
            print(f"  原文本: {text_to_process[:50]}...")
            print(f"  生成标签: {label}")
        
        # 添加延迟避免API限制
        time.sleep(0.5)
    
    # 保存结果
    if output_path is None:
        output_path = file_path.replace('.xlsx', '_processed.xlsx')
    
    try:
        df.to_excel(output_path, index=False)
        print(f"处理完成，结果已保存到: {output_path}")
    except Exception as e:
        print(f"保存文件失败: {str(e)}")

def main():
    """主函数"""
    # 配置参数
    api_key = "sk-2ee194238a9c4a5486709bd13d9a8ffc"
    input_file = "simplified_motivations_mapping.xlsx"
    output_file = "simplified_motivations_mapping_processed.xlsx"
    
    # 处理文件
    process_excel_file(input_file, api_key, output_file)

if __name__ == "__main__":
    main()
