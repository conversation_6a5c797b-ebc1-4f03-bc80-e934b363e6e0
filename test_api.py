#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import requests
import json
import time
import re

class DeepSeekAPI:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_label(self, text: str) -> str:
        """
        使用DeepSeek API将文本转换为机器学习标签
        """
        if not text or not text.strip():
            return ""
        
        # 构建提示词
        prompt = f"""
请将以下文本转换为简洁的机器学习标签格式。要求：
1. 保留核心含义
2. 使用简短的词汇或短语
3. 适合作为分类标签
4. 如果是多个概念，用下划线连接
5. 使用中文，保持简洁

原文本：{text}

标签：
"""
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 50
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                label = result['choices'][0]['message']['content'].strip()
                # 清理标签，去除多余的格式
                label = re.sub(r'^标签[：:]\s*', '', label)
                label = re.sub(r'^Label[：:]\s*', '', label, flags=re.IGNORECASE)
                return label
            else:
                print(f"API请求失败: {response.status_code}, {response.text}")
                return text  # 如果API失败，返回原文本
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return text  # 如果API失败，返回原文本

def main():
    # 配置参数
    api_key = "sk-2ee194238a9c4a5486709bd13d9a8ffc"
    input_file = "simplified_motivations_mapping_cleaned.xlsx"
    
    # 读取清理后的Excel文件
    try:
        df = pd.read_excel(input_file)
        print(f"成功读取清理后的Excel文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return
    
    # 初始化DeepSeek API
    api_client = DeepSeekAPI(api_key)
    
    # 测试前5行数据
    print("\n=== 测试API调用 ===")
    for i in range(1, min(6, len(df))):  # 跳过第一行（可能是空的）
        row = df.iloc[i]
        
        # 获取清理后的文本
        text_to_process = ""
        if pd.notna(row.get('new_label_cleaned')) and isinstance(row['new_label_cleaned'], str) and row['new_label_cleaned'].strip():
            text_to_process = str(row['new_label_cleaned']).strip()
        
        if text_to_process:
            print(f"\n第 {i+1} 行:")
            print(f"原文本: {text_to_process}")
            
            # 生成标签
            label = api_client.generate_label(text_to_process)
            print(f"生成标签: {label}")
            
            # 添加延迟
            time.sleep(2)
        else:
            print(f"第 {i+1} 行: 跳过空行")

if __name__ == "__main__":
    main()
